# HybridSpeechClient 使用指南

## 概述

`HybridSpeechClient` 是一个开箱即用的混合语音识别服务客户端，提供了完整的服务连接、重试机制和协程化的异步接口。

## 主要特性

- ✅ **自动重试机制**：服务绑定失败时自动重试，最多重试3次
- ✅ **应用激活功能**：自动检测并激活处于stopped状态的服务应用
- ✅ **协程化接口**：所有异步操作都使用 Kotlin 协程
- ✅ **连接状态管理**：提供连接状态流，方便监听连接变化
- ✅ **自动回调重注册**：服务重连后自动重新注册回调
- ✅ **统一异常处理**：封装了远程调用异常处理
- ✅ **资源自动清理**：提供完整的资源清理机制

## 快速开始

### 1. 基本使用

```kotlin
// 创建客户端
val client = HybridSpeechClient(context)

// 连接服务
if (client.connectAndWait()) {
    // 开始录音
    val config = RecordingConfig(...)
    client.startRecording(config)
    
    // 停止录音
    val result = client.stopRecording()
    
    // 清理资源
    client.cleanup()
}
```

### 2. 使用工厂类创建

```kotlin
// 创建并自动连接
val client = HybridSpeechClientFactory.createAndConnect(context)
if (client != null) {
    // 使用客户端
    client.startRecording(config)
}
```

### 3. 带回调的完整示例

```kotlin
class MyActivity : AppCompatActivity() {
    private var client: HybridSpeechClient? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建转写回调
        val transcriptionCallback = object : ITranscriptionCallback.Stub() {
            override fun onTranscriptionResult(result: TranscriptionResult) {
                // 处理转写结果
                runOnUiThread {
                    handleTranscriptionResult(result)
                }
            }
            
            override fun onError(errorCode: Int, errorMessage: String?) {
                // 处理错误
                Log.e("MyActivity", "Transcription error: $errorCode - $errorMessage")
            }
        }
        
        // 创建配置提供者
        val configProvider = object : IHybridSpeechConfigProvider.Stub() {
            override fun getNetworkConfig(request: NetworkConfigRequest): NetworkConfig {
                // 返回网络配置
                return createNetworkConfig(request)
            }
        }
        
        // 创建完整配置的客户端
        lifecycleScope.launch {
            client = HybridSpeechClientFactory.createFullyConfigured(
                context = this@MyActivity,
                configProvider = configProvider,
                transcriptionCallback = transcriptionCallback,
                autoConnect = true
            )
        }
    }
    
    private fun startRecording() {
        lifecycleScope.launch {
            try {
                val config = RecordingConfig.createOnlineRecordingConfigWithResumeSupport(
                    recordId = System.currentTimeMillis(),
                    userId = "user123",
                    pcmFilePath = "/path/to/pcm",
                    mp3FilePath = "/path/to/mp3",
                    serverUrl = "",  // 由配置提供者填充
                    apiKey = "",     // 由配置提供者填充
                    language = "cn",
                    audioType = "ogg_opus"
                )
                
                client?.startRecording(config)
            } catch (e: Exception) {
                Log.e("MyActivity", "Failed to start recording", e)
            }
        }
    }
    
    private fun stopRecording() {
        lifecycleScope.launch {
            try {
                val result = client?.stopRecording()
                // 处理录音结果
                result?.let { handleRecordingResult(it) }
            } catch (e: Exception) {
                Log.e("MyActivity", "Failed to stop recording", e)
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        client?.cleanup()
    }
}
```

## 进度监听

```kotlin
// 创建进度流
val progressFlow = client.createProgressFlow(intervalMs = 500)

// 收集进度
lifecycleScope.launch {
    progressFlow.collect { state ->
        when (state) {
            is HybridSpeechClient.RecordingProgressState.Started -> {
                // 录音开始
            }
            is HybridSpeechClient.RecordingProgressState.Progress -> {
                // 录音进度更新
                val durationMs = state.durationMs
                updateProgressUI(durationMs)
            }
            is HybridSpeechClient.RecordingProgressState.Stopped -> {
                // 录音停止
                val totalDuration = state.totalDurationMs
            }
            is HybridSpeechClient.RecordingProgressState.Error -> {
                // 进度错误
                Log.e("Progress", state.message)
            }
        }
    }
}
```

## 连接状态监听

```kotlin
// 监听连接状态
lifecycleScope.launch {
    client.connectionState.collect { isConnected ->
        if (isConnected) {
            // 服务已连接
            enableRecordingUI()
        } else {
            // 服务已断开
            disableRecordingUI()
        }
    }
}
```

## 错误处理

```kotlin
try {
    client.startRecording(config)
} catch (e: IllegalStateException) {
    // 服务未连接
    Log.e("Client", "Service not connected", e)
} catch (e: RemoteException) {
    // 远程调用失败
    Log.e("Client", "Remote call failed", e)
} catch (e: Exception) {
    // 其他错误
    Log.e("Client", "Unexpected error", e)
}
```

## 自定义服务配置

```kotlin
// 连接自定义服务
val client = HybridSpeechClientFactory.createCustomClient(
    context = context,
    servicePackage = "com.example.customservice",
    serviceClass = "com.example.customservice.CustomHybridSpeechService"
)
```

## 最佳实践

1. **使用 ApplicationContext**：避免内存泄漏
2. **及时清理资源**：在 Activity/Fragment 销毁时调用 `cleanup()`
3. **异常处理**：始终包装异步调用在 try-catch 中
4. **生命周期管理**：使用 `lifecycleScope` 管理协程
5. **连接状态检查**：在调用服务方法前检查连接状态

## 注意事项

- 客户端会自动处理服务重连和回调重注册
- 服务绑定失败时会自动重试，最多3次
- 如果目标服务应用处于stopped状态，客户端会尝试激活它
- 所有异步操作都在主线程的协程中执行，适合UI操作
- 记得在不需要时调用 `cleanup()` 释放资源
